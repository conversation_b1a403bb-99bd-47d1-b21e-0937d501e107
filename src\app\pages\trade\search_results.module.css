@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Header Content Styles */
.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.pageSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

@media (max-width: 576px) {
  .headerContent {
    text-align: center;
    padding: 1rem;
  }

  .pageTitle {
    font-size: 24px;
  }

  .pageSubtitle {
    font-size: 13px;
  }
}

/* Compact Trade Container */
.compactTradeContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0.75rem;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

/* Compact Header */
.compactHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tradeBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.tradeAction {
  color: #ffffff;
  font-weight: 700;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
}

.tradeAsset {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 0.75rem;
}

.tradeInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tradePartner {
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
}

.roleBadge {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
}

.orderMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.orderNumber {
  color: #111827;
  font-weight: 700;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.timerCompact {
  color: #f59e0b;
  font-weight: 600;
  font-size: 0.75rem;
}

/* Compact Main Grid */
.compactMainGrid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 0.75rem;
  flex: 1;
  overflow: hidden;
}

/* Left Column */
.leftColumn {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow-y: auto;
  padding-right: 0.25rem;
}

/* Compact Step Card */
.compactStepCard {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
  flex-shrink: 0;
}

.stepHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.stepIcon {
  font-size: 1.125rem;
}

.stepTitle {
  color: #1e40af;
  font-weight: 700;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stepContent {
  margin-top: 0.5rem;
}

.stepText {
  color: #1e40af;
  font-weight: 500;
  font-size: 0.8125rem;
  margin: 0;
  line-height: 1.5;
}

/* Compact Progress */
.compactProgress {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

/* Compact Details */
.compactDetails {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.detailsRow {
  display: grid;
  grid-template-columns: auto 1fr auto 1fr;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detailsRow:last-child {
  margin-bottom: 0;
}

.detailLabel {
  color: #6b7280;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailValue {
  color: #111827;
  font-weight: 600;
  font-size: 0.8125rem;
}

/* Compact Payment */
.compactPayment {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 0.75rem;
  padding: 1rem;
  border-left: 4px solid #f59e0b;
  flex-shrink: 0;
}

.paymentHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.paymentIcon {
  font-size: 1.125rem;
}

.paymentMethod {
  color: #92400e;
  font-weight: 700;
  font-size: 0.875rem;
}

.paymentDetails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.paymentRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.paymentLabel {
  color: #92400e;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.paymentValue {
  color: #78350f;
  font-weight: 600;
  font-size: 0.8125rem;
  text-align: right;
  flex: 1;
  margin-left: 1rem;
}

.uploadNote {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #92400e;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
}

/* Compact Actions */
.compactActions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.primaryBtn {
  flex: 2;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.primaryBtn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
}

.cancelBtn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cancelBtn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
}

.reportBtn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.8125rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.25);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reportBtn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.35);
}

/* Right Column - Compact Chat */
.rightColumn {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.compactChat {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chatHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.chatTitle {
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 700;
}

.chatStatus {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.statusDot {
  width: 6px;
  height: 6px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.statusText {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.6875rem;
  font-weight: 500;
}

.chatContent {
  flex: 1;
  overflow: hidden;
}

/* Responsive Design for Compact Layout */
@media (max-width: 1024px) {
  .compactMainGrid {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .rightColumn {
    order: -1;
    height: 300px;
  }

  .leftColumn {
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .compactTradeContainer {
    padding: 0.5rem;
    height: 100vh;
  }

  .compactHeader {
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .headerLeft,
  .headerRight {
    width: 100%;
    justify-content: space-between;
  }

  .compactMainGrid {
    gap: 0.5rem;
  }

  .leftColumn {
    gap: 0.5rem;
  }

  .compactStepCard,
  .compactProgress,
  .compactDetails,
  .compactPayment {
    padding: 0.75rem;
  }

  .compactActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .primaryBtn,
  .cancelBtn,
  .reportBtn {
    padding: 0.625rem;
    font-size: 0.75rem;
  }

  .rightColumn {
    height: 250px;
  }
}

@media (max-width: 576px) {
  .compactTradeContainer {
    padding: 0.25rem;
    height: 100vh;
  }

  .compactHeader {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .tradeBadge {
    padding: 0.375rem 0.75rem;
  }

  .tradeAction,
  .tradeAsset {
    font-size: 0.6875rem;
  }

  .tradePartner {
    font-size: 0.75rem;
  }

  .orderNumber {
    font-size: 0.75rem;
  }

  .timerCompact {
    font-size: 0.6875rem;
  }

  .compactMainGrid {
    gap: 0.375rem;
  }

  .leftColumn {
    gap: 0.375rem;
  }

  .compactStepCard,
  .compactProgress,
  .compactDetails,
  .compactPayment {
    padding: 0.5rem;
  }

  .stepTitle {
    font-size: 0.75rem;
  }

  .stepText {
    font-size: 0.75rem;
  }

  .detailsRow {
    grid-template-columns: 1fr;
    gap: 0.25rem;
    text-align: left;
  }

  .detailLabel {
    font-size: 0.6875rem;
  }

  .detailValue {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .paymentRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .paymentValue {
    margin-left: 0;
    text-align: left;
  }

  .compactActions {
    gap: 0.375rem;
  }

  .primaryBtn,
  .cancelBtn,
  .reportBtn {
    padding: 0.5rem;
    font-size: 0.6875rem;
  }

  .rightColumn {
    height: 200px;
  }

  .chatHeader {
    padding: 0.5rem 0.75rem;
  }

  .chatTitle {
    font-size: 0.75rem;
  }

  .statusText {
    font-size: 0.625rem;
  }
}

/* Focus and Accessibility */
.primaryBtn:focus,
.cancelBtn:focus,
.reportBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .compactHeader,
  .compactStepCard,
  .compactProgress,
  .compactDetails,
  .compactPayment,
  .compactChat {
    border-width: 2px;
  }

  .primaryBtn,
  .cancelBtn,
  .reportBtn {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .primaryBtn,
  .cancelBtn,
  .reportBtn {
    transition: none;
  }

  .statusDot {
    animation: none;
  }

  .primaryBtn:hover,
  .cancelBtn:hover,
  .reportBtn:hover {
    transform: none;
  }
}

.statusHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.tradeBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.tradeAction {
  color: #ffffff;
  font-weight: 700;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.tradeAsset {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 0.875rem;
}

.tradePartner {
  color: #374151;
  font-weight: 500;
  font-size: 1rem;
}

.statusBody {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.timerSection {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 1.25rem;
  border-radius: 0.75rem;
  border-left: 4px solid #f59e0b;
}

.timerLabel {
  color: #92400e;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timerWrapper {
  color: #78350f;
  font-weight: 700;
  font-size: 1.125rem;
}

.orderInfo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.orderDetail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.orderLabel {
  color: #6b7280;
  font-weight: 500;
  font-size: 0.875rem;
}

.orderValue {
  color: #111827;
  font-weight: 600;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Main Content Grid */
.mainContentGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Trade Progress Section */
.tradeProgressSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Role Badge */
.roleBadgeContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.roleBadge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.senderBadge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
}

.peerBadge {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
}

.roleIcon {
  font-size: 1.25rem;
}

.roleText {
  font-weight: 600;
}

/* Next Step Card */
.nextStepCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.nextStepHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stepIcon {
  font-size: 1.5rem;
}

.nextStepTitle {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
}

.nextStepContent {
  margin-top: 1rem;
}

.stepInstruction {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 1rem;
  border-radius: 0.75rem;
  border-left: 4px solid #3b82f6;
}

.stepInstruction p {
  color: #1e40af;
  font-weight: 500;
  margin: 0;
  line-height: 1.6;
}

/* Progress Section */
.progressSection {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.progressHeader {
  margin-bottom: 1.5rem;
}

.progressTitle {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
}

.stepperWrapper {
  width: 100%;
}

/* Trade Details Card */
.tradeDetailsCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.detailsHeader {
  margin-bottom: 1.5rem;
}

.detailsTitle {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.detailItem:hover {
  background: rgba(243, 244, 246, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detailLabel {
  color: #6b7280;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailValue {
  color: #111827;
  font-weight: 600;
  font-size: 0.875rem;
}

.paymentNotice {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 1rem;
  border-radius: 0.75rem;
  border-left: 4px solid #0ea5e9;
}

.noticeIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.noticeText {
  color: #0c4a6e;
  font-weight: 500;
  margin: 0;
  line-height: 1.6;
  font-size: 0.875rem;
}

/* Payment Information Card */
.paymentInfoCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.paymentHeader {
  margin-bottom: 1.5rem;
}

.paymentTitle {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
}

.paymentContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.paymentMethodSection {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 1.25rem;
  border-radius: 0.75rem;
  border-left: 4px solid #f59e0b;
}

.methodHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.methodIcon {
  font-size: 1.25rem;
}

.methodLabel {
  color: #92400e;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.methodValue {
  color: #78350f;
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 0.75rem;
}

.termsSection {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.termsText {
  color: #92400e;
  font-size: 0.75rem;
  margin: 0;
  line-height: 1.5;
}

.paymentDetailsSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailRow {
  background: rgba(249, 250, 251, 0.8);
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.detailRow:hover {
  background: rgba(243, 244, 246, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detailRowHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.detailIcon {
  font-size: 1.125rem;
}

.detailRowLabel {
  color: #6b7280;
  font-weight: 600;
  font-size: 0.875rem;
}

.detailRowValue {
  color: #111827;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.5;
}

.paymentMethodDetails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.paymentDataItem {
  color: #374151;
  font-size: 0.875rem;
}

.paymentOption {
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
}

.uploadReminder {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  padding: 1rem;
  border-radius: 0.75rem;
  border-left: 4px solid #8b5cf6;
  margin-top: 1rem;
}

.reminderIcon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.reminderText {
  color: #6b21a8;
  font-weight: 500;
  margin: 0;
  line-height: 1.6;
  font-size: 0.875rem;
}

/* Action Buttons Section */
.actionButtonsSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.primaryActions {
  display: flex;
  gap: 1rem;
}

.primaryActionBtn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.primaryActionBtn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.primaryActionBtn:active {
  transform: translateY(0);
}

.cancelActionBtn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cancelActionBtn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.cancelActionBtn:active {
  transform: translateY(0);
}

.reportBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reportBtn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.reportBtn:active {
  transform: translateY(0);
}

.btnIcon {
  font-size: 1.125rem;
}

/* Chat Section */
.chatSection {
  display: flex;
  flex-direction: column;
  height: fit-content;
}

.chatContainer {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chatHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chatTitle {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
}

.chatStatus {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.statusIndicator {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.statusText {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.75rem;
  font-weight: 500;
}

.chatWrapper {
  flex: 1;
  overflow: hidden;
}

/* Modal Wrapper */
.modalWrapper {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .mainContentGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chatSection {
    order: -1;
  }

  .chatContainer {
    height: 400px;
  }

  .statusBody {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .tradeContainer {
    padding: 0.75rem;
  }

  .mobileHeader {
    display: block;
  }

  .tradeStatusCard {
    padding: 1rem;
  }

  .statusHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .tradeBadge {
    padding: 0.5rem 1rem;
  }

  .primaryActions {
    flex-direction: column;
  }

  .primaryActionBtn,
  .cancelActionBtn {
    padding: 0.875rem 1rem;
  }

  .reportBtn {
    padding: 0.875rem 1rem;
  }

  .nextStepCard,
  .progressSection,
  .tradeDetailsCard,
  .paymentInfoCard {
    padding: 1rem;
  }

  .chatContainer {
    height: 350px;
  }
}

@media (max-width: 576px) {
  .tradeContainer {
    padding: 0.5rem;
    background: #ffffff;
  }

  .mobileTitle {
    font-size: 1.25rem;
  }

  .mobileSubtitle {
    font-size: 0.75rem;
  }

  .tradeStatusCard,
  .nextStepCard,
  .progressSection,
  .tradeDetailsCard,
  .paymentInfoCard {
    padding: 0.875rem;
    margin-bottom: 1rem;
  }

  .statusBody {
    gap: 0.75rem;
  }

  .timerSection {
    padding: 0.875rem;
  }

  .orderDetail {
    padding: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detailItem {
    padding: 0.75rem;
  }

  .detailRow {
    padding: 0.75rem;
  }

  .paymentMethodSection {
    padding: 0.875rem;
  }

  .primaryActionBtn,
  .cancelActionBtn,
  .reportBtn {
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .btnIcon {
    font-size: 1rem;
  }

  .chatContainer {
    height: 300px;
    border-radius: 0.75rem;
  }

  .chatHeader {
    padding: 0.75rem 1rem;
  }

  .chatTitle {
    font-size: 0.875rem;
  }

  .statusText {
    font-size: 0.6875rem;
  }
}

/* Focus and Accessibility */
.primaryActionBtn:focus,
.cancelActionBtn:focus,
.reportBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .tradeStatusCard,
  .nextStepCard,
  .progressSection,
  .tradeDetailsCard,
  .paymentInfoCard,
  .chatContainer {
    border-width: 2px;
  }

  .primaryActionBtn,
  .cancelActionBtn,
  .reportBtn {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .primaryActionBtn,
  .cancelActionBtn,
  .reportBtn,
  .detailItem,
  .detailRow {
    transition: none;
  }

  .statusIndicator {
    animation: none;
  }

  .primaryActionBtn:hover,
  .cancelActionBtn:hover,
  .reportBtn:hover,
  .detailItem:hover,
  .detailRow:hover {
    transform: none;
  }
}

.leftContainerWrapper {
    width: 20%;
    position: relative;

    @media screen and (max-width : 576px) {
        display: none;
    }
}

.leftContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;

}

/* need to remove later */

.logoArea {
    height: 20%;
    width: 100%;
    background-color: #4153ed;
    border-radius: 15px 15px 15px 0px;
    display: flex;
    flex-direction: column;
}

.logo {
    margin-top: 5px;
}

.profileBar {
    margin-top: 25px;
}

.profileBarContainer {
    background-color: #4f535a;
    width: 80%;
    height: 35px;
    margin: auto;
    border-radius: 100px;
    background: rgba(255, 255, 255, 0.17);
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.profileImg {
    margin-top: 4px;
    margin-left: 15px;
    margin-right: 5px;
}

.profileName {
    color: #fff;
    font-family: Poppins;
    font-size: 11px;
    font-weight: 500;
}

.profileDropDown {
    margin-left: auto;
    margin-right: 10px;
}


.header {
    color: #000;
    font-family: Poppins;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;

}

.logoContainer {
    /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profileBar {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50%;
}



.rightContainer {
    width: 78%;
    height: auto;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    /* border: 0.5px solid black; */
    height: 100vh;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: auto;
    }
}

.rightContainerWrapper {
    width: 100%;
}

.topBoxWrapper {
    width: 100%;
    border-bottom: 1px dashed #DCDCDC;
    display: flex;
    justify-content: space-between;
    margin-bottom: 80px;
    margin-top: 13px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        margin-bottom: 10px;
    }
}

.topLeftBox {
    width: 50%;
    margin-right: auto;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: 0px 10px;

    }
}

.headerBtn {
    margin-bottom: 10px;
}

.HeaderBuyBtn {
    padding: 8px 15px;
    align-items: center;
    border-radius: 5px;
    border: 0px solid #4153ED;
    background: rgba(65, 83, 237, 0.20);
    margin-right: 10px;
}

.timeSubHeader {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;

    @media screen and (max-width : 576px) {
        display: block;
        display: flex;
    }

}

.topRightBox {
    margin-left: auto;
    justify-content: space-evenly;
    align-items: center;
}

.rightheaderinfo {
    display: flex;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 10px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }
}

.rinfo {
    margin: 0px 10px;
}

.rightheaderinfo1 {
    color: #4153ED;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    margin-left: 11px;
    border-radius: 5px;
    background: #FFF;
    display: inline-flex;
    padding: 5px 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;

}

.bottomBoxWrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }
}

.bottomLeftBox {
    width: 65%;
    background-color: #fff;
    padding: 25px 25px;
    border-radius: 20px;

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 0px
    }
}


.progressBarArea {
    /* height: 30px; */
    margin-bottom: 25px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
}

.confirmOrderInfoArea {
    margin-bottom: 25px;
}

.orderConfirmHeader {
    color: #000;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 16px;
}

.orderConfirmInfo {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 8px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
    }

}

.orderDialogue {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    margin-bottom: 8px;

}

.orderConfirmInfoSingle {
    margin-right: 18px;
}


.paymentInfoContainer {
    width: 100%;
    min-height: 150px;
    display: flex;
    margin-bottom: 20px;
}

.paymentMethodDisplay {
    width: 30%;
    height: 100%;
    background-color: #fff;
    border: 1px solid #EBEBEB;
    background: #FFF;
    padding: 0px 10px;
    border-radius: 5px;
}

.paymentMethod {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 30px;
    padding: 12px 10px;
    background: #F8F8F8;
    color: #4153ED;
    font-family: Poppins;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    margin-top: 10px;
}

.paymentAddressDisplay {
    width: 70%;
    min-height: 150px;
    border: 1px solid #EBEBEB;
    background: #FFF;
}

.paymentAddressName {
    display: flex;
    justify-content: space-between;
    padding: 12px 20px;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;

}

.uploadDialogue {
    color: #4153ED;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

}

.ctaDialogue {
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
}

.activityBtnArea {
    height: 50px;
    display: flex;
    justify-content: space-between;

    @media screen and (max-width : 576px) {
        margin-bottom: 70px;
        display: grid;
    }
}

.leftBtns {
    display: flex;
    width: 50%;
    margin-right: auto;
    cursor: pointer;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 20px;

    }
}

.notifySellerBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #FFE47B;
    margin-right: 10px;
}

.cancelBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #F2F2F2;
}

.reportBtn {
    display: inline-flex;
    height: 40px;
    padding: 8px 20px;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    /* background: #f3769b; */
    background: linear-gradient(#CB356B, #BD3F32);
    color: #fff;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

}

.bottomRightBox {
    height: fit-content;
    width: 30%;
    /* height: 17%; */
    /* background-color: #aa9191; */
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin-bottom: 30px;

    }
}


/* modal */
/* .modalWrapper {
    width: 700px;
} */

.modalHeaderCont {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modalHeader {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

}

.modalHeader2 {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: center;

}

.issueSelect {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;

    display: flex;

}

.issueSelect2 {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;
    justify-content: center;
    display: flex;

}

.optionsBox {
    display: flex;
    flex-direction: column;
}

.inputBoxes {
    margin: 5px 0px;
}

.options {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    margin: 15px 0px;

    /* 50% */
}

.inputTextArea input {
    display: flex;
    width: 80%;
    height: 80px;
    padding: 15px 18px 10px 18px;

    gap: 10px;
    border-radius: 5px;
}

.submitBtnWrapper {
    width: 100%;
    border-radius: 5px;
    background: #4153ED;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
}

.submitBtnWrapper2 {
    width: 100%;
    border-radius: 5px;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.submitBtn {
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    background-color: transparent;
    outline: none;
    border: none;
    cursor: pointer;
}

.disputeClose {
    height: 40px;
    padding: 8px 20px;
    color: #9D9D9D;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border-radius: 5px;
    background: #F2F2F2;
    border: none;
}

.disputeSubmit {
    height: 40px;
    padding: 8px 20px;
    border-radius: 5px;
    background: #4153ED;
    color: #FFF;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.finalSubmitBtn {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #4153ED;
    background: #4153ED;
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;
}

.inputUpload {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    background: #F9F9F9;
}

.passedTerms {
    margin-top: 3px;
    font-size: 12px;
    font-family: poppins;
    font-weight: 500;
    min-height: 150px;
}

.tradePayDetails {
    display: flex;
    list-style-type: none;
    justify-content: flex-start;
    align-items: center;
}

.payLi {
    margin: 5px 5px;
}

.payoutDetailsCont {
    display: flex;
}


.tagArea {
    width: 100%;
    margin-bottom: 25px;
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
}


.senderTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background-color: #fceba8;
    padding: 8px;
    border-radius: 8px;
    font-weight: 600;
    font-family: poppins;
    color: #000;
}

.peerTag {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    background-color: #fceba8;
    padding: 8px;
    border-radius: 8px;
    font-weight: 600;
    font-family: poppins;
    color: #000;
}

.dialogueSteps {
    margin-left: 10px;
    background-color: #4153ED;
    color: #fafafa;
    padding: 5px 20px;
    font-family: poppins;
    font-weight: 700;
    border-radius: 5px;
}