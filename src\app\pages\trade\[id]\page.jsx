"use client";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSearchParams, useParams } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import { useWebsocketContext } from "@/app/context/AuthContext";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import Image from "next/image";
import styles from "../search_results.module.css";
import logoImg from "../../../../../public/rem_logoW.png";
import ProfileImg from "../../../../../public/assets/profile/profileImg.png";
import arrow from "../../../../../public/assets/profile/arrow.png";
import Layout from "../../../components/Layout/page";
import Modal from "react-modal";
import Chat from "../../../components/Chat/page";
import Login from "@/app/sign/login/page";
import "react-toastify/dist/ReactToastify.css";
import TradeTimer from "../../../components/TradeTimer/page";
import TradeStepper from "../../../components/TradeStepper/page";
import DisputeTicket from "../../../components/TradepageModals/DisputeTicket/page";
import ReportTrade from "../../../components/ReportTrade/page";
import TradeReviewModal from "../../../components/TradeReviewModal/page";
import { useSSE } from "@/app/context/SSEContext";
const page = () => {
  const router = useRouter();
  const { event, remflowEvent } = useSSE();
  const { sendMessage } = useWebsocketContext();
  const [uploadPaymentEvidence, setUploadEvidence] = useState("");
  const [timeLeft, setTimeLeft] = useState(null);
  const authTokenRef = useRef(null);
  const firstRenderRef = useRef(null);
  const searchParams = useSearchParams();
  const params = useParams();

  const [tradeData, setTradeData] = useState("");
  const [timeLimit, setTimeLimit] = useState("");
  const [btnNameUser, setBtnNameUser] = useState("");
  const [btnNamePeer, setBtnNamePeer] = useState("");
  const [userTimeLine, setUserTimeLine] = useState("");
  const [peerTimeLine, setPeerTimeLine] = useState("");
  const [tradeDecision, setTradeDecision] = useState("");
  const [payoutDetails, setPayoutDetails] = useState();
  const [activeStep, setActiveStep] = useState("");

  console.log("userTimeLine", userTimeLine);
  // SSE TRade flow
  if (
    remflowEvent.action === "accept_request" &&
    remflowEvent.flag === "user"
  ) {
    setActiveStep(remflowEvent.stepper);
    setBtnNameUser(remflowEvent.button);
    setUserTimeLine(remflowEvent.time_line_state);
    toast.success(remflowEvent.message);
  }
  // SSE TRade flow

  // console.log("event1212", event);
  console.log("remflowEvent12", remflowEvent);
  const passedType = searchParams.get("type");
  const orderNumber = params.id;
  console.log("button", searchParams.get("button"));

  useEffect(() => {
    if (remflowEvent.flag === "user") {
      console.log("userRemlowSSE", {
        stepper: remflowEvent.stepper,
        button: remflowEvent.button,
        time_line_state: remflowEvent.time_line_state,
        message: remflowEvent.message,
      });
      setActiveStep(remflowEvent.stepper);
      setBtnNameUser(remflowEvent.button);
      setUserTimeLine(remflowEvent.time_line_state);
      toast.success(remflowEvent.message);
    } else if (remflowEvent.flag === "peer") {
      console.log("peerRemlowSSE", {
        stepper: remflowEvent.stepper,
        button: remflowEvent.button,
        time_line_state: remflowEvent.time_line_state,
        message: remflowEvent.message,
      });
      setActiveStep(remflowEvent.stepper);
      setBtnNamePeer(remflowEvent.button);
      setPeerTimeLine(remflowEvent.time_line_state);
      toast.success(remflowEvent.message);
    }
  }, [remflowEvent]);

  const getPeerDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/peer/data/?order_id=${orderNumber}`
      );
      console.log("resqq", res);
      setPeerTimeLine(res.data.data.time_line_state);
      setBtnNamePeer(res.data.data.button);
      setActiveStep(res.data.data.stepper);
      toast.success(res.data.message);
    } catch (error) {
      console.error("resqq", error);
    }
  };
  const getUserDetailsByOrderId = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/trade/history/user/data/?order_id=${orderNumber}`
      );
      console.log("resqqq", res);
      setBtnNameUser(res.data.data.button);
      setUserTimeLine(res.data.data.time_line_state);
      setActiveStep(res.data.data.stepper);
      toast.success(res.data.message);
    } catch (error) {
      console.error("resqqqq", error);
    }
  };

  if (passedType !== "user") {
    useEffect(() => {
      getPeerDetailsByOrderId();
    }, []);
  }

  if (passedType === "user") {
    useEffect(() => {
      getUserDetailsByOrderId();
    }, []);
  }

  let username1;
  if (typeof window !== "undefined") {
    username1 = localStorage.getItem("userName");
  }

  // special refersh tokent
  const fetchPeerDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-peer-details/");
      setTelegramId(res.data.data.telegram_id);
      setWhatsappId(res.data.data.whatsapp_id);
      setWeChatId(res.data.data.wechat_id);
      setOtherMesesgingId(res.data.data.any_other_id);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    fetchPeerDetailsApi();
  }, []);

  const [modalIsOpen, setIsOpen] = useState(false);
  const [modalIsOpen2, setIsOpen2] = useState(false);
  const [modalIsOpen3, setIsOpen3] = useState(false);
  const [modalIsOpen4, setIsOpen4] = useState(false);
  const [modalIsOpen5, setIsOpen5] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [telegramId, setTelegramId] = useState("");
  const [whatsappId, setWhatsappId] = useState("");
  const [weChatId, setWeChatId] = useState("");
  const [otherMesesgingId, setOtherMesesgingId] = useState("");

  const openModal = () => {
    setIsOpen(!modalIsOpen);
  };
  const openModal2 = () => {
    setIsOpen2(!modalIsOpen2);
    setIsOpen(false);
  };
  const openModal3 = () => {
    setIsOpen3(!modalIsOpen3);
  };
  const openModal4 = () => {
    setIsOpen4(!modalIsOpen4);
  };
  const openModal5 = () => {
    setIsOpen5(!modalIsOpen4);
  };
  // const openModal4 = () => {
  //   setIsOpen3(!modalIsOpen3);
  //   setIsOpen2(false);
  //   setIsOpen(false);
  // };

  const afterOpenModal = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal2 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal3 = () => {
    // references are now sync'd and can be accessed.
  };
  const afterOpenModal4 = () => {
    // references are now sync'd and can be accessed.
  };

  const closeModal = () => {
    setIsOpen(false);
  };
  const closeModal2 = () => {
    setIsOpen(false);
  };
  const closeModal3 = () => {
    setIsOpen3(false);
  };
  const closeModal4 = () => {
    setIsOpen3(false);
  };

  const formData = new FormData();
  formData.append("order_number", orderNumber);
  formData.append(" evidence", uploadPaymentEvidence);

  const formDataNotifyPeer = new FormData();
  formDataNotifyPeer.append("order_number", orderNumber);

  const handleCancelTrade = async () => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );

      if (res.status === 200) {
        toast.success(res.data.message);
        // router.push("/pages/searchads");
      }
    } catch (error) {
      console.error("error", error);
    }
    // setTimeout(() => {
    //   router.push("/pages/searchads");
    // }, 1000);
  };
  const handleCancelTradePeer = async() => {
    const payload = {
      order_id: orderNumber,
    };
    try {
      const res = await customFetchWithToken.post(
        "/trade/cancel-order/",
        payload
      );
      console.log("res", res);
      if (res.status === 200) {
        toast.success(res.data.message);
        // router.push("/pages/searchads");
      }
    } catch (error) {
      console.error("error", error);
    }
    // setTimeout(() => {
    //   router.push("/pages/searchads");
    // }, 1000);
  };

  const paymentRecivedByPeerApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(res.data.data.stepper);
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendMoneyToUserApi = async () => {
    const payload = {
      order_id: orderNumber,
    };

    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-user/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "peer") {
        // toast.success(res.data.message);
        setBtnNamePeer(res.data.data.button);
        setPeerTimeLine(res.data.data.time_line_state);
        setActiveStep(res.data.data.stepper);
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromPeerToSender = async () => {
    if (peerTimeLine === "received_payment_by_peer") {
      paymentRecivedByPeerApi();
    }
    if (peerTimeLine === "send_to_user") {
      sendMoneyToUserApi();
    }
    if (peerTimeLine === "Paid_money_to_User") {
      const payload = {
        action: "initiate_payment_to_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Payment_Received_by_User") {
      const payload = {
        action: "done_by_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Trade_Completed") {
      const payload = {
        action: "done_by_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
      setShowReviewModal(true);
    }
  };
  console.log("userTimeLine", userTimeLine);

  const sendMoneyToPeerApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/send-money-to-peer/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(res.data.data.stepper);
      }
    } catch (error) {
      console.error("error", error);
    }
  };
  const paymentRecivedByUserApi = async () => {
    const payload = { order_id: orderNumber };
    try {
      const res = await customFetchWithToken.post(
        "/trade/received-payment-by-user/",
        payload
      );
      console.log("res", res);
      if (res.status === 200 && res.data.data.flag === "user") {
        // toast.success(res.data.message);
        setBtnNameUser(res.data.data.button);
        setUserTimeLine(res.data.data.time_line_state);
        setActiveStep(res.data.data.stepper);
        setTimeout(() => {
          setShowReviewModal(true);
        }, 1000);
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  const sendPaymentFromSenderToPeer = async () => {
    if (userTimeLine === "pay_to_peer") {
      sendMoneyToPeerApi();
    }

    if (userTimeLine === "received_payment_by_user") {
      paymentRecivedByUserApi()
    }
    if (userTimeLine === "Trade_Completed") {
      const payload = {
        action: "done_by_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));

      setShowReviewModal(true);
    }
  };



 

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  if (!token) {
    router.push("/sign/login");
  }
  if (token) {
    authTokenRef.current = token;
  }

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "15px",
      width: "400px",
    },
  };
  Modal.setAppElement("body");
  let subtitle;

  const tradeTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Trade #{orderNumber}</h1>
      <p className={styles.pageSubtitle}>
        {passedType === "user" ? "You are the sender" : "You are the peer"} • Active Trade Session
      </p>
    </div>
  );

  return (
    <>
      {authTokenRef.current ? (
        <Layout title={tradeTitle}>
          <div className={styles.compactTradeContainer}>
            {/* Compact Header */}
            <div className={styles.compactHeader}>
              <div className={styles.headerLeft}>
                <div className={styles.tradeBadge}>
                  <span className={styles.tradeAction}>BUY</span>
                  <span className={styles.tradeAsset}>USDT</span>
                </div>
                <div className={styles.tradeInfo}>
                  <span className={styles.tradePartner}>
                    from {tradeData?.flag === "user"
                      ? tradeData?.peer_id?.username
                      : tradeData?.user_details?.username}
                  </span>
                  <div className={styles.roleBadge}>
                    {passedType === "user" ? "📤 Sender" : "📥 Peer"}
                  </div>
                </div>
              </div>
              <div className={styles.headerRight}>
                <div className={styles.orderMeta}>
                  <span className={styles.orderNumber}>#{orderNumber}</span>
                  <div className={styles.timerCompact}>
                    <TradeTimer
                      duration={timeLimit?.left_time_in_milliseconds}
                      orderNumber={orderNumber}
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* Compact Main Content */}
            <div className={styles.compactMainGrid}>
              {/* Left Column - Trade Details & Progress */}
              <div className={styles.leftColumn}>
                {/* Compact Next Step */}
                <div className={styles.compactStepCard}>
                  <div className={styles.stepHeader}>
                    <span className={styles.stepIcon}>🎯</span>
                    <span className={styles.stepTitle}>Next Step</span>
                  </div>
                  <div className={styles.stepContent}>
                    {activeStep === 1 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Click Pay to Peer to initialize payment" :
                          "Wait for user to initialize payment"}
                      </p>
                    )}
                    {activeStep === 2 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Make payment then click Paid to Peer" :
                          "Wait for user to confirm payment"}
                      </p>
                    )}
                    {activeStep === 3 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Wait for peer to mark payment received" :
                          "Click Payment Received to confirm"}
                      </p>
                    )}
                    {activeStep === 4 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Wait for peer to initialize payment to you" :
                          "Click Pay to User to send payment"}
                      </p>
                    )}
                    {activeStep === 5 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Wait for peer to confirm payment" :
                          "Make payment then click Paid to User"}
                      </p>
                    )}
                    {activeStep === 6 && (
                      <p className={styles.stepText}>
                        {passedType === "user" ?
                          "Click Payment Received to confirm" :
                          "Wait for user to confirm payment received"}
                      </p>
                    )}
                    {activeStep === 7 && (
                      <p className={styles.stepText}>Click Trade Completed to finish</p>
                    )}
                  </div>
                </div>

                {/* Compact Progress */}
                <div className={styles.compactProgress}>
                  <TradeStepper activeStep={activeStep} />
                </div>

                {/* Compact Trade Details */}
                <div className={styles.compactDetails}>
                  <div className={styles.detailsRow}>
                    <span className={styles.detailLabel}>Pair:</span>
                    <span className={styles.detailValue}>
                      {tradeData?.currency_from}-{tradeData?.currency_to}
                    </span>
                    <span className={styles.detailLabel}>Rate:</span>
                    <span className={styles.detailValue}>
                      {tradeData?.listing_data?.final_trade_fee?.toFixed(2)}
                    </span>
                  </div>
                  <div className={styles.detailsRow}>
                    <span className={styles.detailLabel}>Amount:</span>
                    <span className={styles.detailValue}>{tradeData?.trade_amount}</span>
                    <span className={styles.detailLabel}>Limit:</span>
                    <span className={styles.detailValue}>
                      {tradeData?.listing_data?.min_liquidity}-{tradeData?.listing_data?.max_liquidity}
                    </span>
                  </div>
                </div>
                {/* Compact Payment Info */}
                <div className={styles.compactPayment}>
                  <div className={styles.paymentHeader}>
                    <span className={styles.paymentIcon}>💳</span>
                    <span className={styles.paymentMethod}>{tradeData?.listing_data?.payin_option}</span>
                  </div>
                  <div className={styles.paymentDetails}>
                    <div className={styles.paymentRow}>
                      <span className={styles.paymentLabel}>To:</span>
                      <span className={styles.paymentValue}>
                        {tradeData?.flag === "user" ? (
                          `${tradeData?.peer_id?.firstname} ${tradeData?.peer_id?.lastname}`
                        ) : (
                          `${tradeData?.user_details?.firstname} ${tradeData?.user_details?.lastname}`
                        )}
                      </span>
                    </div>
                    <div className={styles.paymentRow}>
                      <span className={styles.paymentLabel}>Method:</span>
                      <span className={styles.paymentValue}>
                        {tradeData?.flag === "peer" ?
                          tradeData?.data?.payout_option :
                          tradeData?.data?.payin_option}
                      </span>
                    </div>
                  </div>
                  <div className={styles.uploadNote}>
                    📸 Upload receipt in chat to confirm payment
                  </div>
                </div>

                {/* Compact Action Buttons */}
                <div className={styles.compactActions}>
                  {tradeData?.flag === "peer" || (!tradeData?.flag && !passedType) ? (
                    <>
                      <button
                        className={styles.primaryBtn}
                        onClick={sendPaymentFromPeerToSender}
                      >
                        {btnNamePeer}
                      </button>
                      <button
                        className={styles.cancelBtn}
                        onClick={handleCancelTradePeer}
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        className={styles.primaryBtn}
                        onClick={sendPaymentFromSenderToPeer}
                      >
                        {btnNameUser}
                      </button>
                      <button
                        className={styles.cancelBtn}
                        onClick={handleCancelTrade}
                      >
                        Cancel
                      </button>
                    </>
                  )}
                  <button className={styles.reportBtn} onClick={openModal}>
                    Report
                  </button>
                </div>
              </div>

              {/* Right Column - Compact Chat */}
              <div className={styles.rightColumn}>
                <div className={styles.compactChat}>
                  <div className={styles.chatHeader}>
                    <span className={styles.chatTitle}>💬 Trade Chat</span>
                    <div className={styles.chatStatus}>
                      <div className={styles.statusDot}></div>
                      <span className={styles.statusText}>Online</span>
                    </div>
                  </div>
                  <div className={styles.chatContent}>
                    <Chat Recipientname={username1} orderNumber={orderNumber} />
                  </div>
                </div>
              </div>
            </div>
            {/* Modals */}
            <Modal
              className={styles.modalWrapper}
              isOpen={modalIsOpen}
              onAfterOpen={afterOpenModal}
              onRequestClose={closeModal}
              style={customStyles}
              contentLabel="Report Trade Modal"
            >
              <ReportTrade
                openModal2={openModal2}
                closeModal={closeModal}
                orderNumber={orderNumber}
              />
            </Modal>

            <Modal
              className={styles.modalWrapper}
              isOpen={modalIsOpen2}
              onAfterOpen={afterOpenModal2}
              onRequestClose={closeModal2}
              style={customStyles}
              contentLabel="Dispute Ticket Modal"
            >
              <DisputeTicket setIsOpen2={setIsOpen2} />
            </Modal>

            <Modal
              className={styles.modalWrapper}
              isOpen={modalIsOpen3}
              onAfterOpen={afterOpenModal3}
              onRequestClose={closeModal3}
              style={customStyles}
              contentLabel="Upload Evidence Modal"
            >
              {/* Upload Evidence Component */}
            </Modal>

            <Modal
              className={styles.modalWrapper}
              isOpen={showReviewModal}
              onRequestClose={() => setShowReviewModal(false)}
              style={customStyles}
              contentLabel="Trade Review Modal"
            >
              <TradeReviewModal
                onClose={() => setShowReviewModal(false)}
                orderNumber={orderNumber}
                username={username1 || "Trader"}
              />
            </Modal>

            <ToastContainer
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
          </div>
        </Layout>
      ) : (
        <Login />
      )}
    </>
  );
};

export default page;
